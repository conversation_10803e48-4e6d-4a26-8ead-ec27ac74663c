#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gerador de Audiobooks usando Kokoro TTS
Integra processamento de texto com síntese de voz
"""

import os
import sys
import time
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Generator
import logging
import soundfile as sf
import numpy as np

# Importar módulos locais
from processador_notas import ProcessadorNotasObsidian
from utils import ProgressoTarefa, formatar_duracao, obter_tamanho_arquivo

# Configurar logging
logger = logging.getLogger(__name__)


class GeradorAudiobook:
    """
    Gerador principal de audiobooks usando Kokoro TTS
    """
    
    def __init__(self, configuracoes: Optional[Dict] = None):
        """
        Inicializa o gerador de audiobooks
        
        Args:
            configuracoes: Dicionário com configurações personalizadas
        """
        self.configuracoes = self._configuracoes_padrao()
        if configuracoes:
            self.configuracoes.update(configuracoes)
        
        self.processador_notas = ProcessadorNotasObsidian()
        self.pipeline_tts = None
        self._inicializar_tts()
    
    def _configuracoes_padrao(self) -> Dict:
        """Retorna configurações padrão do sistema"""
        return {
            'lang_code': 'p',  # Português brasileiro
            'voice': 'af_heart',  # Voz padrão
            'speed': 1.0,  # Velocidade normal
            'sample_rate': 24000,  # Taxa de amostragem do Kokoro
            'max_palavras_segmento': 200,  # Máximo de palavras por segmento
            'pausa_entre_segmentos': 0.5,  # Pausa em segundos entre segmentos
            'normalizar_volume': True,  # Normalizar volume do áudio
            'formato_saida': 'wav',  # Formato de saída
        }
    
    def _inicializar_tts(self):
        """Inicializa o pipeline do Kokoro TTS"""
        try:
            logger.info("🔄 Inicializando Kokoro TTS...")
            from kokoro import KPipeline
            
            self.pipeline_tts = KPipeline(lang_code=self.configuracoes['lang_code'])
            logger.info("✅ Kokoro TTS inicializado com sucesso!")
            
        except ImportError as e:
            logger.error(f"❌ Erro ao importar Kokoro TTS: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Kokoro TTS: {e}")
            raise
    
    def gerar_audio_segmento(self, texto: str) -> np.ndarray:
        """
        Gera áudio para um segmento de texto
        
        Args:
            texto: Texto a ser convertido em áudio
            
        Returns:
            Array numpy com dados de áudio
        """
        if not texto.strip():
            # Retornar silêncio para texto vazio
            duracao_silencio = 0.5  # 0.5 segundos
            amostras_silencio = int(self.configuracoes['sample_rate'] * duracao_silencio)
            return np.zeros(amostras_silencio, dtype=np.float32)
        
        try:
            # Gerar áudio usando Kokoro TTS
            generator = self.pipeline_tts(
                texto,
                voice=self.configuracoes['voice'],
                speed=self.configuracoes['speed'],
                split_pattern=r'\n+'
            )
            
            # Coletar todos os segmentos de áudio
            segmentos_audio = []
            for _, _, audio in generator:
                segmentos_audio.append(audio)
            
            if not segmentos_audio:
                # Retornar silêncio se não houver áudio gerado
                duracao_silencio = 0.5
                amostras_silencio = int(self.configuracoes['sample_rate'] * duracao_silencio)
                return np.zeros(amostras_silencio, dtype=np.float32)
            
            # Concatenar segmentos
            audio_completo = np.concatenate(segmentos_audio)
            
            # Normalizar volume se configurado
            if self.configuracoes['normalizar_volume']:
                audio_completo = self._normalizar_volume(audio_completo)
            
            return audio_completo
            
        except Exception as e:
            logger.error(f"Erro ao gerar áudio para segmento: {e}")
            # Retornar silêncio em caso de erro
            duracao_silencio = 1.0
            amostras_silencio = int(self.configuracoes['sample_rate'] * duracao_silencio)
            return np.zeros(amostras_silencio, dtype=np.float32)
    
    def _normalizar_volume(self, audio: np.ndarray, target_level: float = 0.7) -> np.ndarray:
        """
        Normaliza o volume do áudio
        
        Args:
            audio: Array de áudio
            target_level: Nível alvo (0.0 a 1.0)
            
        Returns:
            Áudio normalizado
        """
        if len(audio) == 0:
            return audio
        
        # Encontrar pico máximo
        max_val = np.max(np.abs(audio))
        
        if max_val > 0:
            # Normalizar para o nível alvo
            audio_normalizado = audio * (target_level / max_val)
            return audio_normalizado
        
        return audio
    
    def _adicionar_pausa(self, duracao: float) -> np.ndarray:
        """
        Cria um segmento de silêncio
        
        Args:
            duracao: Duração da pausa em segundos
            
        Returns:
            Array de silêncio
        """
        amostras = int(self.configuracoes['sample_rate'] * duracao)
        return np.zeros(amostras, dtype=np.float32)
    
    def gerar_audiobook_arquivo(self, caminho_arquivo: Path, pasta_saida: Path) -> Dict:
        """
        Gera audiobook para um arquivo específico
        
        Args:
            caminho_arquivo: Caminho do arquivo .md
            pasta_saida: Pasta onde salvar o audiobook
            
        Returns:
            Informações sobre o audiobook gerado
        """
        logger.info(f"🎵 Gerando audiobook para: {caminho_arquivo.name}")
        
        inicio = time.time()
        
        # Processar arquivo
        info_arquivo = self.processador_notas.processar_arquivo(caminho_arquivo)
        
        # Dividir em segmentos
        segmentos = self.processador_notas.dividir_texto_em_segmentos(
            info_arquivo['conteudo_limpo'],
            max_palavras=self.configuracoes['max_palavras_segmento']
        )
        
        logger.info(f"📝 Texto dividido em {len(segmentos)} segmentos")
        
        # Gerar áudio para cada segmento
        progresso = ProgressoTarefa(len(segmentos), f"Gerando áudio - {caminho_arquivo.name}")
        
        segmentos_audio = []
        for i, segmento in enumerate(segmentos):
            try:
                audio_segmento = self.gerar_audio_segmento(segmento)
                segmentos_audio.append(audio_segmento)
                
                # Adicionar pausa entre segmentos (exceto no último)
                if i < len(segmentos) - 1:
                    pausa = self._adicionar_pausa(self.configuracoes['pausa_entre_segmentos'])
                    segmentos_audio.append(pausa)
                
                progresso.atualizar()
                
            except Exception as e:
                logger.error(f"Erro no segmento {i+1}: {e}")
                # Adicionar silêncio em caso de erro
                silencio = self._adicionar_pausa(1.0)
                segmentos_audio.append(silencio)
                progresso.atualizar()
        
        # Concatenar todos os segmentos
        logger.info("🔗 Concatenando segmentos de áudio...")
        audio_completo = np.concatenate(segmentos_audio) if segmentos_audio else np.array([])
        
        # Salvar arquivo de áudio
        pasta_saida.mkdir(parents=True, exist_ok=True)
        nome_arquivo = f"{info_arquivo['nome']}.{self.configuracoes['formato_saida']}"
        caminho_saida = pasta_saida / nome_arquivo
        
        logger.info(f"💾 Salvando audiobook: {caminho_saida}")
        sf.write(str(caminho_saida), audio_completo, self.configuracoes['sample_rate'])
        
        # Calcular estatísticas
        duracao_audio = len(audio_completo) / self.configuracoes['sample_rate']
        tempo_processamento = time.time() - inicio
        
        info_audiobook = {
            'arquivo_origem': str(caminho_arquivo),
            'arquivo_saida': str(caminho_saida),
            'nome': info_arquivo['nome'],
            'palavras': info_arquivo['palavras'],
            'segmentos': len(segmentos),
            'duracao_audio': duracao_audio,
            'duracao_formatada': formatar_duracao(duracao_audio),
            'tempo_processamento': tempo_processamento,
            'tempo_processamento_formatado': formatar_duracao(tempo_processamento),
            'tamanho_arquivo': obter_tamanho_arquivo(caminho_saida),
            'taxa_palavras_por_minuto': info_arquivo['palavras'] / (duracao_audio / 60) if duracao_audio > 0 else 0
        }
        
        logger.info(f"✅ Audiobook gerado com sucesso!")
        logger.info(f"   📊 Duração: {info_audiobook['duracao_formatada']}")
        logger.info(f"   📊 Tamanho: {info_audiobook['tamanho_arquivo']}")
        logger.info(f"   ⏱️ Tempo de processamento: {info_audiobook['tempo_processamento_formatado']}")
        
        return info_audiobook

    def gerar_audiobook_pasta(self, pasta_origem: Path, pasta_saida: Path) -> List[Dict]:
        """
        Gera audiobooks para todos os arquivos de uma pasta

        Args:
            pasta_origem: Pasta com arquivos .md
            pasta_saida: Pasta onde salvar audiobooks

        Returns:
            Lista com informações de todos os audiobooks gerados
        """
        logger.info(f"📁 Processando pasta: {pasta_origem}")

        # Processar todos os arquivos da pasta
        arquivos_processados = self.processador_notas.processar_pasta(pasta_origem)

        if not arquivos_processados:
            logger.warning("Nenhum arquivo encontrado para processar")
            return []

        audiobooks_gerados = []
        progresso_geral = ProgressoTarefa(len(arquivos_processados), "Gerando audiobooks")

        for info_arquivo in arquivos_processados:
            try:
                caminho_arquivo = Path(info_arquivo['caminho_original'])
                info_audiobook = self.gerar_audiobook_arquivo(caminho_arquivo, pasta_saida)
                audiobooks_gerados.append(info_audiobook)

            except Exception as e:
                logger.error(f"Falha ao gerar audiobook para {info_arquivo['nome']}: {e}")
                continue

            finally:
                progresso_geral.atualizar()

        # Estatísticas finais
        total_duracao = sum(ab['duracao_audio'] for ab in audiobooks_gerados)
        total_palavras = sum(ab['palavras'] for ab in audiobooks_gerados)

        logger.info(f"🎉 Processamento concluído!")
        logger.info(f"   📚 Audiobooks gerados: {len(audiobooks_gerados)}")
        logger.info(f"   ⏱️ Duração total: {formatar_duracao(total_duracao)}")
        logger.info(f"   📝 Total de palavras: {total_palavras:,}")

        return audiobooks_gerados

    def configurar(self, **kwargs):
        """
        Atualiza configurações do gerador

        Args:
            **kwargs: Configurações a serem atualizadas
        """
        self.configuracoes.update(kwargs)
        logger.info(f"Configurações atualizadas: {kwargs}")

        # Reinicializar TTS se necessário
        if 'lang_code' in kwargs:
            self._inicializar_tts()


def main():
    """Função principal para teste do módulo"""
    import sys

    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

    # Teste básico
    try:
        gerador = GeradorAudiobook()

        # Testar com arquivo específico
        arquivo_teste = Path("../📝Resumos/Administrativo/01 - Regime Jurídico Administrativo.md")

        if arquivo_teste.exists():
            pasta_saida = Path("output/audiobooks")
            info_audiobook = gerador.gerar_audiobook_arquivo(arquivo_teste, pasta_saida)

            print("\n🎉 Teste concluído com sucesso!")
            print(f"📁 Audiobook salvo em: {info_audiobook['arquivo_saida']}")
        else:
            print(f"❌ Arquivo de teste não encontrado: {arquivo_teste}")

    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
