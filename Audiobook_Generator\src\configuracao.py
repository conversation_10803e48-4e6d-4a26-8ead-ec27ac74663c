#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Configuração para Gerador de Audiobooks
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)


class ConfiguradorAudiobook:
    """
    Gerenciador de configurações do sistema de audiobooks
    """
    
    def __init__(self, arquivo_config: Optional[Path] = None):
        """
        Inicializa o configurador
        
        Args:
            arquivo_config: Caminho para arquivo de configuração personalizado
        """
        self.arquivo_config = arquivo_config or Path("config/config.yaml")
        self.configuracoes = self._carregar_configuracoes()
    
    def _configuracoes_padrao(self) -> Dict[str, Any]:
        """Retorna configurações padrão do sistema"""
        return {
            # Configurações do Kokoro TTS
            'tts': {
                'lang_code': 'p',  # Português brasileiro
                'voice': 'af_heart',  # Voz padrão feminina
                'speed': 1.0,  # Velocidade normal
                'sample_rate': 24000,  # Taxa de amostragem
                'normalizar_volume': True,
                'target_volume': 0.7,
            },
            
            # Configurações de processamento
            'processamento': {
                'max_palavras_segmento': 200,
                'pausa_entre_segmentos': 0.5,
                'formato_saida': 'wav',
                'qualidade_audio': 'alta',
            },
            
            # Caminhos padrão
            'caminhos': {
                'obsidian_vault': str(Path("../").resolve()),
                'pasta_resumos': "📝Resumos",
                'pasta_saida': "output/audiobooks",
                'pasta_temp': "temp",
                'pasta_logs': "logs",
            },
            
            # Configurações de interface
            'interface': {
                'mostrar_progresso': True,
                'nivel_log': 'INFO',
                'salvar_logs': True,
                'confirmar_sobrescrita': True,
            },
            
            # Vozes disponíveis por categoria
            'vozes_disponiveis': {
                'femininas': ['af_heart', 'af_sky', 'af_bella'],
                'masculinas': ['am_adam', 'am_michael'],
                'neutras': ['af_sarah', 'am_alex'],
            },
            
            # Configurações avançadas
            'avancado': {
                'usar_gpu': True,
                'batch_size': 1,
                'cache_modelos': True,
                'otimizar_memoria': False,
            }
        }
    
    def _carregar_configuracoes(self) -> Dict[str, Any]:
        """Carrega configurações do arquivo ou usa padrões"""
        configuracoes = self._configuracoes_padrao()
        
        if self.arquivo_config.exists():
            try:
                with open(self.arquivo_config, 'r', encoding='utf-8') as f:
                    config_arquivo = yaml.safe_load(f)
                
                if config_arquivo:
                    # Mesclar configurações (arquivo sobrescreve padrões)
                    configuracoes = self._mesclar_dicionarios(configuracoes, config_arquivo)
                    logger.info(f"Configurações carregadas de: {self.arquivo_config}")
                
            except Exception as e:
                logger.warning(f"Erro ao carregar configurações: {e}")
                logger.info("Usando configurações padrão")
        
        else:
            logger.info("Arquivo de configuração não encontrado, usando padrões")
        
        return configuracoes
    
    def _mesclar_dicionarios(self, base: Dict, atualizacao: Dict) -> Dict:
        """
        Mescla dois dicionários recursivamente
        
        Args:
            base: Dicionário base
            atualizacao: Dicionário com atualizações
            
        Returns:
            Dicionário mesclado
        """
        resultado = base.copy()
        
        for chave, valor in atualizacao.items():
            if chave in resultado and isinstance(resultado[chave], dict) and isinstance(valor, dict):
                resultado[chave] = self._mesclar_dicionarios(resultado[chave], valor)
            else:
                resultado[chave] = valor
        
        return resultado
    
    def salvar_configuracoes(self):
        """Salva configurações atuais no arquivo"""
        try:
            # Criar diretório se não existir
            self.arquivo_config.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.arquivo_config, 'w', encoding='utf-8') as f:
                yaml.dump(self.configuracoes, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info(f"Configurações salvas em: {self.arquivo_config}")
            
        except Exception as e:
            logger.error(f"Erro ao salvar configurações: {e}")
            raise
    
    def obter(self, chave: str, padrao: Any = None) -> Any:
        """
        Obtém valor de configuração usando notação de ponto
        
        Args:
            chave: Chave da configuração (ex: 'tts.voice')
            padrao: Valor padrão se chave não existir
            
        Returns:
            Valor da configuração
        """
        try:
            valor = self.configuracoes
            for parte in chave.split('.'):
                valor = valor[parte]
            return valor
        except (KeyError, TypeError):
            return padrao
    
    def definir(self, chave: str, valor: Any):
        """
        Define valor de configuração usando notação de ponto
        
        Args:
            chave: Chave da configuração (ex: 'tts.voice')
            valor: Novo valor
        """
        partes = chave.split('.')
        config_atual = self.configuracoes
        
        # Navegar até o penúltimo nível
        for parte in partes[:-1]:
            if parte not in config_atual:
                config_atual[parte] = {}
            config_atual = config_atual[parte]
        
        # Definir valor final
        config_atual[partes[-1]] = valor
        logger.debug(f"Configuração atualizada: {chave} = {valor}")
    
    def obter_configuracoes_tts(self) -> Dict[str, Any]:
        """Retorna configurações específicas para o TTS"""
        return {
            'lang_code': self.obter('tts.lang_code'),
            'voice': self.obter('tts.voice'),
            'speed': self.obter('tts.speed'),
            'sample_rate': self.obter('tts.sample_rate'),
            'normalizar_volume': self.obter('tts.normalizar_volume'),
            'max_palavras_segmento': self.obter('processamento.max_palavras_segmento'),
            'pausa_entre_segmentos': self.obter('processamento.pausa_entre_segmentos'),
            'formato_saida': self.obter('processamento.formato_saida'),
        }
    
    def obter_caminhos(self) -> Dict[str, Path]:
        """Retorna caminhos configurados como objetos Path"""
        caminhos = {}
        for chave, valor in self.obter('caminhos', {}).items():
            caminhos[chave] = Path(valor)
        return caminhos
    
    def listar_vozes_disponiveis(self) -> Dict[str, list]:
        """Retorna lista de vozes disponíveis por categoria"""
        return self.obter('vozes_disponiveis', {})
    
    def validar_configuracoes(self) -> List[str]:
        """
        Valida configurações atuais
        
        Returns:
            Lista de erros encontrados (vazia se tudo OK)
        """
        erros = []
        
        # Validar configurações TTS
        lang_code = self.obter('tts.lang_code')
        if lang_code not in ['a', 'b', 'e', 'f', 'h', 'i', 'j', 'p', 'z']:
            erros.append(f"Código de idioma inválido: {lang_code}")
        
        speed = self.obter('tts.speed')
        if not isinstance(speed, (int, float)) or speed <= 0:
            erros.append(f"Velocidade inválida: {speed}")
        
        # Validar caminhos
        caminhos = self.obter_caminhos()
        vault_path = caminhos.get('obsidian_vault')
        if vault_path and not vault_path.exists():
            erros.append(f"Caminho do Obsidian Vault não encontrado: {vault_path}")
        
        # Validar formato de saída
        formato = self.obter('processamento.formato_saida')
        if formato not in ['wav', 'mp3', 'flac']:
            erros.append(f"Formato de saída não suportado: {formato}")
        
        return erros
    
    def criar_configuracao_exemplo(self):
        """Cria arquivo de configuração de exemplo"""
        exemplo_path = self.arquivo_config.parent / "config_exemplo.yaml"
        
        try:
            with open(exemplo_path, 'w', encoding='utf-8') as f:
                f.write("""# Configuração do Gerador de Audiobooks Kokoro TTS
# Este é um arquivo de exemplo - copie para config.yaml e personalize

# Configurações do Text-to-Speech
tts:
  lang_code: 'p'          # Português brasileiro
  voice: 'af_heart'       # Voz feminina padrão
  speed: 1.0              # Velocidade normal (0.5 - 2.0)
  sample_rate: 24000      # Taxa de amostragem
  normalizar_volume: true # Normalizar volume do áudio
  target_volume: 0.7      # Nível de volume alvo (0.0 - 1.0)

# Configurações de processamento
processamento:
  max_palavras_segmento: 200    # Máximo de palavras por segmento
  pausa_entre_segmentos: 0.5    # Pausa em segundos entre segmentos
  formato_saida: 'wav'          # Formato de saída (wav, mp3, flac)
  qualidade_audio: 'alta'       # Qualidade do áudio

# Caminhos do sistema
caminhos:
  obsidian_vault: '../'                    # Caminho para o Obsidian Vault
  pasta_resumos: '📝Resumos'              # Pasta com resumos
  pasta_saida: 'output/audiobooks'        # Pasta de saída
  pasta_temp: 'temp'                      # Pasta temporária
  pasta_logs: 'logs'                      # Pasta de logs

# Configurações da interface
interface:
  mostrar_progresso: true      # Mostrar barra de progresso
  nivel_log: 'INFO'           # Nível de logging (DEBUG, INFO, WARNING, ERROR)
  salvar_logs: true           # Salvar logs em arquivo
  confirmar_sobrescrita: true # Confirmar antes de sobrescrever arquivos

# Configurações avançadas
avancado:
  usar_gpu: true              # Tentar usar GPU se disponível
  batch_size: 1               # Tamanho do batch para processamento
  cache_modelos: true         # Fazer cache dos modelos carregados
  otimizar_memoria: false     # Otimizar uso de memória
""")
            
            logger.info(f"Arquivo de exemplo criado: {exemplo_path}")
            
        except Exception as e:
            logger.error(f"Erro ao criar arquivo de exemplo: {e}")


def main():
    """Função de teste do configurador"""
    print("Testando sistema de configuração...")
    
    configurador = ConfiguradorAudiobook()
    
    # Mostrar algumas configurações
    print(f"Idioma TTS: {configurador.obter('tts.lang_code')}")
    print(f"Voz: {configurador.obter('tts.voice')}")
    print(f"Velocidade: {configurador.obter('tts.speed')}")
    
    # Validar configurações
    erros = configurador.validar_configuracoes()
    if erros:
        print("Erros encontrados:")
        for erro in erros:
            print(f"  - {erro}")
    else:
        print("✅ Configurações válidas!")
    
    # Criar arquivo de exemplo
    configurador.criar_configuracao_exemplo()
    
    print("Teste concluído!")


if __name__ == "__main__":
    main()
