# 🚀 Guia de Instalação - Gerador de Audiobooks Kokoro TTS

## 📋 Pré-requisitos

### Sistema Operacional
- ✅ Windows 10/11 (testado e recomendado)
- ✅ Linux (compatível)
- ✅ macOS (compatível)

### Software Necessário
- 🐍 **Python 3.9 ou superior**
- 📦 **pip** (gerenciador de pacotes Python)
- 🎵 **espeak-ng** (para fallback de fonemas)

## 🔧 Instalação Passo a Passo

### 1. Verificar Python

```bash
python --version
# Deve mostrar Python 3.9+ 
```

Se não tiver Python instalado:
- Windows: Baixe de [python.org](https://python.org)
- Linux: `sudo apt install python3 python3-pip`
- macOS: `brew install python3`

### 2. Baixar o Projeto

```bash
# Clone ou baixe o projeto para uma pasta
cd "C:\Obsidian Vault\Audiobook_Generator"
```

### 3. Instalar Dependências

```bash
# Instalar todas as dependências automaticamente
pip install -r requirements.txt
```

**Dependências principais instaladas:**
- `kokoro>=0.9.4` - Modelo TTS
- `torch>=2.0.0` - Framework de IA
- `torchaudio>=2.0.0` - Processamento de áudio
- `soundfile>=0.12.1` - Manipulação de arquivos de áudio
- `pyyaml>=6.0` - Configurações
- `click>=8.1.0` - Interface CLI
- `tqdm>=4.65.0` - Barras de progresso

### 4. Configuração Inicial

```bash
# Executar configuração automática
python main.py setup
```

Este comando irá:
- ✅ Criar estrutura de diretórios
- ✅ Baixar modelo Kokoro TTS (~327MB)
- ✅ Criar arquivo de configuração exemplo
- ✅ Testar funcionamento do sistema

### 5. Verificar Instalação

```bash
# Ver configurações do sistema
python main.py config

# Testar com arquivo simples
python main.py arquivo "exemplo.md"
```

## 🎯 Teste Rápido

### Criar Arquivo de Teste

Crie um arquivo `teste.md`:

```markdown
# Meu Primeiro Audiobook

Este é um teste do sistema de audiobooks.

## Funcionalidades

- Converte texto em áudio
- Usa voz brasileira natural
- Processa formatações automaticamente

Teste concluído com sucesso!
```

### Gerar Audiobook

```bash
python main.py arquivo "teste.md"
```

**Resultado esperado:**
- ✅ Arquivo `output/audiobooks/teste.wav` criado
- ✅ Duração: ~30 segundos
- ✅ Qualidade: 24kHz, 16-bit

## ⚙️ Configuração Avançada

### Arquivo de Configuração

Copie `config/config_exemplo.yaml` para `config/config.yaml` e personalize:

```yaml
# Configurações do TTS
tts:
  voice: 'af_heart'    # Voz feminina (padrão)
  speed: 1.0           # Velocidade normal
  
# Caminhos personalizados
caminhos:
  obsidian_vault: 'C:\Meu\Obsidian\Vault'
  pasta_saida: 'D:\Meus\Audiobooks'
```

### Vozes Disponíveis

```bash
# Ver todas as vozes
python main.py config

# Testar voz específica
python main.py arquivo "teste.md" --voice af_sky
```

**Vozes femininas:** `af_heart`, `af_sky`, `af_bella`
**Vozes masculinas:** `am_adam`, `am_michael`

## 🔧 Solução de Problemas

### Erro: "ModuleNotFoundError: No module named 'kokoro'"

**Solução:**
```bash
pip install kokoro>=0.9.4
```

### Erro: "espeak-ng not found"

**Windows:**
1. Baixe [espeak-ng](https://github.com/espeak-ng/espeak-ng/releases)
2. Instale o arquivo `.msi`
3. Reinicie o terminal

**Linux:**
```bash
sudo apt install espeak-ng
```

### Erro: GPU AMD não detectada

**Não é problema!** O sistema funciona perfeitamente com CPU.

Para usar GPU AMD (opcional):
- Linux: Instale ROCm
- Windows: Use CPU (recomendado)

### Processamento muito lento

**Soluções:**
1. Reduza arquivos grandes em partes menores
2. Ajuste `max_palavras_segmento: 100` na configuração
3. Use `--speed 1.2` para áudio mais rápido

### Áudio muito baixo

**Solução:**
```yaml
tts:
  normalizar_volume: true
  target_volume: 0.8  # Aumentar volume
```

## 📊 Performance Esperada

### Benchmarks Típicos
- **Arquivo pequeno** (500 palavras): ~15 segundos
- **Arquivo médio** (2000 palavras): ~1 minuto
- **Arquivo grande** (5000 palavras): ~3 minutos

### Qualidade do Áudio
- **Taxa de amostragem:** 24kHz
- **Formato:** WAV 16-bit
- **Qualidade:** Comparável a TTS comerciais
- **Naturalidade:** Excelente para português brasileiro

## 🎉 Pronto para Usar!

Após a instalação, você pode:

```bash
# Processar um arquivo
python main.py arquivo "minha_nota.md"

# Processar pasta inteira
python main.py pasta "📝Resumos/Administrativo"

# Usar voz masculina
python main.py arquivo "nota.md" --voice am_adam

# Velocidade mais rápida
python main.py arquivo "nota.md" --speed 1.3
```

## 📞 Suporte

Se encontrar problemas:

1. **Verifique os logs:** Use `python main.py -v arquivo teste.md`
2. **Teste configurações:** Execute `python main.py config`
3. **Execute testes:** Execute `python teste_completo.py`

## 🔄 Atualizações

Para atualizar o sistema:

```bash
# Atualizar dependências
pip install --upgrade kokoro torch torchaudio

# Verificar nova versão
python main.py --version
```

---

**🎧 Agora você pode transformar todas as suas notas do Obsidian em audiobooks de qualidade profissional!**
