{"presets": {}, "globalQuery": "", "globalFilter": "", "removeGlobalFilter": false, "taskFormat": "tasksPluginEmoji", "setCreatedDate": false, "setDoneDate": true, "setCancelledDate": true, "autoSuggestInEditor": true, "autoSuggestMinMatch": 0, "autoSuggestMaxItems": 6, "provideAccessKeys": true, "useFilenameAsScheduledDate": false, "filenameAsScheduledDateFormat": "", "filenameAsDateFolders": [], "recurrenceOnNextLine": false, "removeScheduledDateOnRecurrence": false, "statusSettings": {"coreStatuses": [{"symbol": " ", "name": "Todo", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "TODO"}, {"symbol": "x", "name": "Done", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "DONE"}], "customStatuses": [{"symbol": "/", "name": "In Progress", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "IN_PROGRESS"}, {"symbol": "-", "name": "Cancelled", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "CANCELLED"}, {"symbol": " ", "name": "Unchecked", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "x", "name": "Checked", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "DONE"}, {"symbol": ">", "name": "Rescheduled", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "<", "name": "Scheduled", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "!", "name": "Important", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "?", "name": "Question", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "*", "name": "Star", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "n", "name": "Note", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "l", "name": "Location", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "i", "name": "Information", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "I", "name": "Idea", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "S", "name": "Amount", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "p", "name": "Pro", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "c", "name": "Con", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "b", "name": "Bookmark", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "\"", "name": "Quote", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "0", "name": "Speech bubble 0", "nextStatusSymbol": "0", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "1", "name": "Speech bubble 1", "nextStatusSymbol": "1", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "2", "name": "Speech bubble 2", "nextStatusSymbol": "2", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "3", "name": "Speech bubble 3", "nextStatusSymbol": "3", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "4", "name": "Speech bubble 4", "nextStatusSymbol": "4", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "5", "name": "Speech bubble 5", "nextStatusSymbol": "5", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "6", "name": "Speech bubble 6", "nextStatusSymbol": "6", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "7", "name": "Speech bubble 7", "nextStatusSymbol": "7", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "8", "name": "Speech bubble 8", "nextStatusSymbol": "8", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "9", "name": "Speech bubble 9", "nextStatusSymbol": "9", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": " ", "name": "incomplete", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "x", "name": "complete / done", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "DONE"}, {"symbol": "-", "name": "cancelled", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "CANCELLED"}, {"symbol": ">", "name": "deferred", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "/", "name": "in progress, or half-done", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "IN_PROGRESS"}, {"symbol": "?", "name": "question", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "R", "name": "review", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "+", "name": "Inbox / task that should be processed later", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "b", "name": "bookmark", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "B", "name": "brainstorm", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "D", "name": "deferred or scheduled", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "I", "name": "Info", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "i", "name": "idea", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "N", "name": "note", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "Q", "name": "quote", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "W", "name": "win / success / reward", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "P", "name": "pro", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "C", "name": "con", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "f", "name": "Fire", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "k", "name": "Key", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "w", "name": "Win", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "u", "name": "Up", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "d", "name": "Down", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": " ", "name": "to-do", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "/", "name": "incomplete", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "IN_PROGRESS"}, {"symbol": "x", "name": "done", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "DONE"}, {"symbol": "-", "name": "canceled", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "CANCELLED"}, {"symbol": ">", "name": "forwarded", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "<", "name": "scheduling", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "!", "name": "important", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "*", "name": "star", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "\"", "name": "quote", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "l", "name": "location", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "i", "name": "information", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "S", "name": "savings", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "I", "name": "idea", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "p", "name": "pros", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "c", "name": "cons", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "f", "name": "fire", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "k", "name": "key", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "w", "name": "win", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "u", "name": "up", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "d", "name": "down", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "x", "name": "Regular", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "DONE"}, {"symbol": "X", "name": "Checked", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "DONE"}, {"symbol": "-", "name": "Dropped", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "CANCELLED"}, {"symbol": ">", "name": "Forward", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "D", "name": "Date", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "/", "name": "Half Done", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "IN_PROGRESS"}, {"symbol": "+", "name": "Add", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "R", "name": "Research", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "i", "name": "Idea", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "B", "name": "Brainstorm", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "P", "name": "Pro", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "C", "name": "Con", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "Q", "name": "Quote", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "N", "name": "Note", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "I", "name": "Information", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "p", "name": "Paraphrase", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "L", "name": "Location", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "E", "name": "Example", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "A", "name": "Answer", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "r", "name": "<PERSON><PERSON>", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "c", "name": "Choice", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "d", "name": "Doing", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "IN_PROGRESS"}, {"symbol": "T", "name": "Time", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "@", "name": "Character / Person", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "t", "name": "Talk", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "O", "name": "Outline / Plot", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "~", "name": "Conflict", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "W", "name": "World", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "f", "name": "Clue / Find", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "F", "name": "Foreshadow", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "H", "name": "Favorite / Health", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "&", "name": "Symbolism", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "s", "name": "Secret", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}]}, "features": {"INTERNAL_TESTING_ENABLED_BY_DEFAULT": true}, "generalSettings": {}, "headingOpened": {"Core Statuses": true, "Custom Statuses": true}, "debugSettings": {"ignoreSortInstructions": false, "showTaskHiddenData": false, "recordTimings": false}, "loggingOptions": {"minLevels": {"": "info", "tasks": "info", "tasks.Cache": "info", "tasks.Events": "info", "tasks.File": "info", "tasks.Query": "info", "tasks.Task": "info"}}}