# Plano de Implementação - Sistema de Audiobooks Kokoro TTS

## Análise Técnica Completa

### ✅ Compatibilidade Verificada
- **Kokoro TTS**: Suporte nativo ao Português do Brasil (código 'p')
- **AMD GPU**: Compatível via PyTorch + ROCm (Linux) ou CPU fallback (Windows)
- **Modelo**: 82M parâmetros, Apache License, qualidade comparável a modelos maiores
- **Vozes**: 54 vozes disponíveis em 8 idiomas

### 🎯 Objetivos do Sistema
1. Converter notas Obsidian (.md) para audiobooks (.wav)
2. Processar automaticamente formatações HTML/CSS/Markdown
3. Manter estrutura hierárquica através de pausas naturais
4. Gerar áudio de alta qualidade em português brasileiro
5. Interface simples de linha de comando

## Arquitetura do Sistema

### 📁 Estrutura de Arquivos
```
Audiobook_Generator/
├── venv/                          # Ambiente virtual Python
├── src/
│   ├── __init__.py
│   ├── processador_notas.py       # Limpeza e processamento de notas
│   ├── gerador_audiobook.py       # Geração de áudio com Kokoro
│   ├── configuracao.py            # Configurações do sistema
│   └── utils.py                   # Utilitários gerais
├── config/
│   └── config.yaml                # Arquivo de configuração
├── output/                        # Audiobooks gerados
├── temp/                          # Arquivos temporários
├── requirements.txt               # Dependências Python
├── main.py                        # Script principal
└── README.md                      # Documentação
```

### 🔧 Componentes Principais

#### 1. ProcessadorNotas
- Remove formatações HTML/CSS/Markdown
- Preserva estrutura hierárquica
- Divide texto em segmentos para processamento
- Aplica regras específicas para TTS

#### 2. GeradorAudiobook
- Integração com Kokoro TTS
- Controle de velocidade e pausas
- Concatenação de segmentos de áudio
- Otimização para GPU AMD (se disponível)

#### 3. Sistema de Configuração
- Seleção de voz brasileira
- Configurações de qualidade de áudio
- Mapeamento de pastas origem/destino
- Parâmetros de processamento

## Especificações Técnicas

### 🐍 Dependências Python
```
kokoro>=0.9.4
torch>=2.0.0
torchaudio>=2.0.0
soundfile>=0.12.1
pyyaml>=6.0
click>=8.1.0
tqdm>=4.65.0
```

### 🎵 Configurações de Áudio
- **Taxa de Amostragem**: 24kHz (padrão Kokoro)
- **Formato**: WAV 16-bit
- **Voz Padrão**: Voz brasileira feminina
- **Velocidade**: Configurável (0.5x - 2.0x)

### 📝 Processamento de Texto
- Remove tags HTML: `<span>`, `</span>`, etc.
- Remove formatações Markdown: `**`, `*`, `#`, etc.
- Preserva quebras de linha significativas
- Adiciona pausas entre seções
- Normaliza espaçamentos

## Fluxo de Execução

### 1. Inicialização
- Carrega configurações
- Verifica dependências
- Inicializa modelo Kokoro
- Detecta GPU AMD (se disponível)

### 2. Processamento
- Escaneia pasta de notas Obsidian
- Processa cada arquivo .md
- Limpa formatações
- Divide em segmentos

### 3. Geração de Áudio
- Converte texto para áudio via Kokoro
- Aplica configurações de voz/velocidade
- Concatena segmentos
- Salva arquivo final

### 4. Pós-processamento
- Normaliza volume
- Adiciona metadados
- Organiza arquivos de saída

## Próximos Passos

### Fase 1: Configuração do Ambiente ✅
- [x] Análise de compatibilidade
- [ ] Criação do ambiente virtual
- [ ] Instalação de dependências

### Fase 2: Desenvolvimento Core
- [ ] Implementar ProcessadorNotas
- [ ] Implementar GeradorAudiobook
- [ ] Sistema de configuração

### Fase 3: Interface e Testes
- [ ] CLI interface
- [ ] Testes com diferentes tipos de notas
- [ ] Otimização de performance

### Fase 4: Finalização
- [ ] Documentação completa
- [ ] Scripts de instalação
- [ ] Validação final

## Considerações Especiais

### 🔥 Otimização AMD GPU
- Verificação automática de ROCm
- Fallback para CPU se necessário
- Configuração de batch size otimizada

### 📚 Processamento de Notas Obsidian
- Suporte a formatações complexas
- Preservação de estrutura hierárquica
- Tratamento de caracteres especiais

### 🎧 Qualidade de Áudio
- Configurações otimizadas para estudo
- Pausas naturais entre seções
- Volume normalizado

## Estimativa de Tempo
- **Configuração**: 30 minutos
- **Desenvolvimento**: 4-6 horas
- **Testes**: 2 horas
- **Total**: 6-8 horas
