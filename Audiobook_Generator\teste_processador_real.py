#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do processador com arquivo real do Obsidian
"""

import sys
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent / "src"))

from processador_notas import ProcessadorNotasObsidian

def main():
    """Testa processador com arquivo real"""
    processador = ProcessadorNotasObsidian()
    
    # Caminho para um arquivo real do Obsidian
    arquivo_teste = Path("../📝Resumos/Administrativo/01 - Regime Jurídico Administrativo.md")
    
    if not arquivo_teste.exists():
        print(f"❌ Arquivo não encontrado: {arquivo_teste}")
        return
    
    print("🔄 Processando arquivo real do Obsidian...")
    
    try:
        # Processar arquivo
        info_arquivo = processador.processar_arquivo(arquivo_teste)
        
        print(f"✅ Arquivo processado: {info_arquivo['nome']}")
        print(f"📊 Estatísticas:")
        print(f"   - Tamanho original: {info_arquivo['tamanho_original']} caracteres")
        print(f"   - Tamanho limpo: {info_arquivo['tamanho_limpo']} caracteres")
        print(f"   - Palavras: {info_arquivo['palavras']}")
        print(f"   - Linhas: {info_arquivo['linhas']}")
        
        # Salvar texto limpo para verificação
        pasta_temp = Path("temp")
        arquivo_limpo = processador.salvar_texto_limpo(info_arquivo, pasta_temp)
        
        print(f"💾 Texto limpo salvo em: {arquivo_limpo}")
        
        # Mostrar preview do texto limpo
        print("\n📝 Preview do texto limpo (primeiras 500 caracteres):")
        print("-" * 50)
        preview = info_arquivo['conteudo_limpo'][:500]
        print(preview)
        if len(info_arquivo['conteudo_limpo']) > 500:
            print("...")
        print("-" * 50)
        
        # Testar divisão em segmentos
        segmentos = processador.dividir_texto_em_segmentos(info_arquivo['conteudo_limpo'], max_palavras=100)
        print(f"\n🔀 Texto dividido em {len(segmentos)} segmentos")
        
        for i, segmento in enumerate(segmentos[:3]):  # Mostrar apenas os 3 primeiros
            palavras = len(segmento.split())
            print(f"   Segmento {i+1}: {palavras} palavras")
            print(f"   Preview: {segmento[:100]}...")
            print()
        
        print("🎉 Teste concluído com sucesso!")
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
