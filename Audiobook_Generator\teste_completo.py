#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste Completo do Sistema de Audiobooks
Testa todas as funcionalidades e gera relatório de performance
"""

import sys
import time
import json
from pathlib import Path
from typing import Dict, List

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent / "src"))

from gerador_audiobook import GeradorAudiobook
from configuracao import ConfiguradorAudiobook
from processador_notas import ProcessadorNotasObsidian
from utils import formatar_duracao, obter_tamanho_arquivo

class TestadorSistema:
    """Classe para testar o sistema completo"""
    
    def __init__(self):
        self.configurador = ConfiguradorAudiobook()
        self.resultados = []
        self.inicio_teste = time.time()
    
    def criar_arquivos_teste(self) -> List[Path]:
        """Cria arquivos de teste com diferentes características"""
        pasta_teste = Path("temp/testes")
        pasta_teste.mkdir(parents=True, exist_ok=True)
        
        arquivos_teste = []
        
        # Teste 1: Arquivo simples
        arquivo1 = pasta_teste / "01_simples.md"
        with open(arquivo1, 'w', encoding='utf-8') as f:
            f.write("""# Teste Simples

Este é um teste básico do sistema.

O texto é curto e direto.""")
        arquivos_teste.append(arquivo1)
        
        # Teste 2: Arquivo com formatações
        arquivo2 = pasta_teste / "02_formatacoes.md"
        with open(arquivo2, 'w', encoding='utf-8') as f:
            f.write("""## Teste com Formatações

### Definição
<span class="definicao">Este texto tem formatação HTML</span>

**Características:**
* Item com **negrito**
* Item com *itálico*
* Item com `código`

---

### Atenção!
<span class="atencao">Texto importante destacado</span>

[Link para algo](http://exemplo.com)""")
        arquivos_teste.append(arquivo2)
        
        # Teste 3: Arquivo médio
        arquivo3 = pasta_teste / "03_medio.md"
        with open(arquivo3, 'w', encoding='utf-8') as f:
            f.write("""# Direito Administrativo - Conceitos Básicos

## Introdução

O Direito Administrativo é um ramo do direito público que regula a organização e o funcionamento da Administração Pública, bem como suas relações com os particulares.

### Princípios Fundamentais

#### Legalidade
A Administração Pública só pode fazer o que a lei permite ou autoriza. Este princípio é fundamental para garantir que o poder público seja exercido dentro dos limites legais.

#### Impessoalidade
Os atos administrativos devem ser praticados sem favoritismo ou perseguição, visando sempre o interesse público.

#### Moralidade
A conduta administrativa deve pautar-se por padrões éticos de probidade, decoro e boa-fé.

#### Publicidade
Os atos administrativos devem ser públicos, permitindo o controle pela sociedade.

#### Eficiência
A Administração deve buscar os melhores resultados com o menor custo possível.

## Organização Administrativa

### Administração Direta
Compreende os órgãos que integram a estrutura administrativa da União, Estados, Distrito Federal e Municípios.

### Administração Indireta
Formada por entidades com personalidade jurídica própria, como autarquias, fundações, empresas públicas e sociedades de economia mista.

## Conclusão

O estudo do Direito Administrativo é essencial para compreender o funcionamento do Estado e a proteção dos direitos dos cidadãos.""")
        arquivos_teste.append(arquivo3)
        
        return arquivos_teste
    
    def testar_processamento_texto(self) -> Dict:
        """Testa o processamento de texto"""
        print("🔄 Testando processamento de texto...")
        
        processador = ProcessadorNotasObsidian()
        
        # Texto com formatações complexas
        texto_teste = """
        ## Título Principal
        
        ### Subtítulo
        <span class="definicao">Definição importante</span>
        
        **Lista de itens:**
        * Item 1 com **negrito**
        * Item 2 com *itálico*
        * Item 3 com `código`
        
        ---
        
        <span class="atencao">Atenção especial</span>
        
        [Link](http://exemplo.com) e [[Link interno]]
        """
        
        inicio = time.time()
        texto_limpo = processador.limpar_texto(texto_teste)
        tempo_processamento = time.time() - inicio
        
        # Verificar se formatações foram removidas
        formatacoes_removidas = all([
            '<span' not in texto_limpo,
            '**' not in texto_limpo,
            '*' not in texto_limpo,
            '`' not in texto_limpo,
            '---' not in texto_limpo,
            '[' not in texto_limpo,
            ']' not in texto_limpo,
            '##' not in texto_limpo,
        ])
        
        return {
            'teste': 'processamento_texto',
            'sucesso': formatacoes_removidas,
            'tempo': tempo_processamento,
            'tamanho_original': len(texto_teste),
            'tamanho_limpo': len(texto_limpo),
            'reducao_percentual': ((len(texto_teste) - len(texto_limpo)) / len(texto_teste)) * 100
        }
    
    def testar_geracao_audio(self, arquivos_teste: List[Path]) -> List[Dict]:
        """Testa geração de áudio para diferentes tipos de arquivo"""
        print("🎵 Testando geração de áudio...")
        
        config_tts = self.configurador.obter_configuracoes_tts()
        gerador = GeradorAudiobook(config_tts)
        
        resultados_audio = []
        pasta_saida = Path("temp/testes_audio")
        
        for arquivo in arquivos_teste:
            print(f"   Processando: {arquivo.name}")
            
            inicio = time.time()
            try:
                info_audiobook = gerador.gerar_audiobook_arquivo(arquivo, pasta_saida)
                tempo_total = time.time() - inicio
                
                resultado = {
                    'teste': f'audio_{arquivo.stem}',
                    'arquivo': arquivo.name,
                    'sucesso': True,
                    'palavras': info_audiobook['palavras'],
                    'duracao_audio': info_audiobook['duracao_audio'],
                    'tempo_processamento': tempo_total,
                    'tamanho_arquivo': info_audiobook['tamanho_arquivo'],
                    'segmentos': info_audiobook['segmentos'],
                    'taxa_palavras_minuto': info_audiobook['taxa_palavras_por_minuto'],
                    'arquivo_gerado': info_audiobook['arquivo_saida']
                }
                
            except Exception as e:
                resultado = {
                    'teste': f'audio_{arquivo.stem}',
                    'arquivo': arquivo.name,
                    'sucesso': False,
                    'erro': str(e),
                    'tempo_processamento': time.time() - inicio
                }
            
            resultados_audio.append(resultado)
        
        return resultados_audio
    
    def testar_diferentes_vozes(self) -> List[Dict]:
        """Testa diferentes vozes disponíveis"""
        print("🎤 Testando diferentes vozes...")
        
        vozes_teste = ['af_heart', 'af_sky', 'am_adam']
        texto_teste = "Este é um teste de diferentes vozes do sistema Kokoro TTS."
        
        resultados_vozes = []
        
        for voz in vozes_teste:
            print(f"   Testando voz: {voz}")
            
            try:
                config_tts = self.configurador.obter_configuracoes_tts()
                config_tts['voice'] = voz
                
                gerador = GeradorAudiobook(config_tts)
                
                inicio = time.time()
                audio = gerador.gerar_audio_segmento(texto_teste)
                tempo = time.time() - inicio
                
                resultado = {
                    'teste': f'voz_{voz}',
                    'voz': voz,
                    'sucesso': True,
                    'tempo_geracao': tempo,
                    'tamanho_audio': len(audio),
                    'duracao_estimada': len(audio) / 24000  # 24kHz
                }
                
            except Exception as e:
                resultado = {
                    'teste': f'voz_{voz}',
                    'voz': voz,
                    'sucesso': False,
                    'erro': str(e)
                }
            
            resultados_vozes.append(resultado)
        
        return resultados_vozes
    
    def testar_configuracoes(self) -> Dict:
        """Testa sistema de configurações"""
        print("⚙️ Testando sistema de configurações...")
        
        inicio = time.time()
        
        try:
            # Testar carregamento
            config_original = self.configurador.obter('tts.voice')
            
            # Testar modificação
            self.configurador.definir('tts.voice', 'af_sky')
            config_modificada = self.configurador.obter('tts.voice')
            
            # Restaurar original
            self.configurador.definir('tts.voice', config_original)
            
            # Testar validação
            erros = self.configurador.validar_configuracoes()
            
            tempo = time.time() - inicio
            
            return {
                'teste': 'configuracoes',
                'sucesso': config_modificada == 'af_sky',
                'tempo': tempo,
                'erros_validacao': len(erros),
                'config_funcional': len(erros) == 0
            }
            
        except Exception as e:
            return {
                'teste': 'configuracoes',
                'sucesso': False,
                'erro': str(e),
                'tempo': time.time() - inicio
            }
    
    def executar_todos_testes(self) -> Dict:
        """Executa todos os testes e gera relatório"""
        print("🚀 Iniciando testes completos do sistema...")
        print("=" * 60)
        
        # Criar arquivos de teste
        arquivos_teste = self.criar_arquivos_teste()
        
        # Executar testes
        resultado_texto = self.testar_processamento_texto()
        self.resultados.append(resultado_texto)
        
        resultados_audio = self.testar_geracao_audio(arquivos_teste)
        self.resultados.extend(resultados_audio)
        
        resultados_vozes = self.testar_diferentes_vozes()
        self.resultados.extend(resultados_vozes)
        
        resultado_config = self.testar_configuracoes()
        self.resultados.append(resultado_config)
        
        # Gerar relatório
        tempo_total = time.time() - self.inicio_teste
        
        relatorio = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'tempo_total': tempo_total,
            'tempo_total_formatado': formatar_duracao(tempo_total),
            'total_testes': len(self.resultados),
            'testes_sucesso': sum(1 for r in self.resultados if r.get('sucesso', False)),
            'testes_falha': sum(1 for r in self.resultados if not r.get('sucesso', False)),
            'resultados': self.resultados
        }
        
        return relatorio
    
    def salvar_relatorio(self, relatorio: Dict):
        """Salva relatório em arquivo JSON"""
        arquivo_relatorio = Path("temp/relatorio_testes.json")
        arquivo_relatorio.parent.mkdir(exist_ok=True)
        
        with open(arquivo_relatorio, 'w', encoding='utf-8') as f:
            json.dump(relatorio, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Relatório salvo em: {arquivo_relatorio}")
    
    def imprimir_relatorio(self, relatorio: Dict):
        """Imprime relatório formatado"""
        print("\n" + "=" * 60)
        print("📊 RELATÓRIO DE TESTES")
        print("=" * 60)
        
        print(f"⏰ Data/Hora: {relatorio['timestamp']}")
        print(f"⏱️ Tempo total: {relatorio['tempo_total_formatado']}")
        print(f"📈 Testes executados: {relatorio['total_testes']}")
        print(f"✅ Sucessos: {relatorio['testes_sucesso']}")
        print(f"❌ Falhas: {relatorio['testes_falha']}")
        
        taxa_sucesso = (relatorio['testes_sucesso'] / relatorio['total_testes']) * 100
        print(f"📊 Taxa de sucesso: {taxa_sucesso:.1f}%")
        
        print("\n📋 DETALHES DOS TESTES:")
        print("-" * 40)
        
        for resultado in relatorio['resultados']:
            status = "✅" if resultado.get('sucesso', False) else "❌"
            nome_teste = resultado.get('teste', 'Desconhecido')
            print(f"{status} {nome_teste}")
            
            if not resultado.get('sucesso', False) and 'erro' in resultado:
                print(f"   Erro: {resultado['erro']}")
        
        print("\n🎉 Testes concluídos!")


def main():
    """Função principal"""
    testador = TestadorSistema()
    
    try:
        relatorio = testador.executar_todos_testes()
        testador.imprimir_relatorio(relatorio)
        testador.salvar_relatorio(relatorio)
        
        # Verificar se todos os testes passaram
        if relatorio['testes_falha'] == 0:
            print("\n🎉 Todos os testes passaram! Sistema pronto para uso.")
            return 0
        else:
            print(f"\n⚠️ {relatorio['testes_falha']} teste(s) falharam. Verifique os detalhes acima.")
            return 1
            
    except Exception as e:
        print(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
