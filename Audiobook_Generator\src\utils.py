#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utilitários para o Sistema de Audiobooks
"""

import os
import sys
import time
from pathlib import Path
from typing import List, Dict, Optional, Union
import logging

# Configurar logging
logger = logging.getLogger(__name__)


def configurar_logging(nivel: str = "INFO", arquivo_log: Optional[Path] = None):
    """
    Configura o sistema de logging
    
    Args:
        nivel: Nível de logging (DEBUG, INFO, WARNING, ERROR)
        arquivo_log: Caminho para arquivo de log (opcional)
    """
    nivel_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR
    }
    
    formato = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    if arquivo_log:
        logging.basicConfig(
            level=nivel_map.get(nivel, logging.INFO),
            format=formato,
            handlers=[
                logging.FileHandler(arquivo_log, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    else:
        logging.basicConfig(
            level=nivel_map.get(nivel, logging.INFO),
            format=formato,
            stream=sys.stdout
        )


def validar_caminho(caminho: Union[str, Path], deve_existir: bool = True) -> Path:
    """
    Valida e converte caminho para Path
    
    Args:
        caminho: Caminho a ser validado
        deve_existir: Se True, verifica se o caminho existe
        
    Returns:
        Path validado
        
    Raises:
        FileNotFoundError: Se deve_existir=True e caminho não existe
        ValueError: Se caminho é inválido
    """
    if not caminho:
        raise ValueError("Caminho não pode ser vazio")
    
    path_obj = Path(caminho)
    
    if deve_existir and not path_obj.exists():
        raise FileNotFoundError(f"Caminho não encontrado: {path_obj}")
    
    return path_obj


def criar_estrutura_diretorios(base_dir: Path, subdirs: List[str]) -> Dict[str, Path]:
    """
    Cria estrutura de diretórios
    
    Args:
        base_dir: Diretório base
        subdirs: Lista de subdiretórios a criar
        
    Returns:
        Dicionário com caminhos criados
    """
    caminhos = {"base": base_dir}
    
    base_dir.mkdir(parents=True, exist_ok=True)
    
    for subdir in subdirs:
        caminho_subdir = base_dir / subdir
        caminho_subdir.mkdir(parents=True, exist_ok=True)
        caminhos[subdir] = caminho_subdir
        logger.debug(f"Diretório criado: {caminho_subdir}")
    
    return caminhos


def obter_tamanho_arquivo(caminho: Path) -> str:
    """
    Obtém tamanho do arquivo em formato legível
    
    Args:
        caminho: Caminho do arquivo
        
    Returns:
        Tamanho formatado (ex: "1.5 MB")
    """
    if not caminho.exists():
        return "0 B"
    
    tamanho = caminho.stat().st_size
    
    for unidade in ['B', 'KB', 'MB', 'GB']:
        if tamanho < 1024.0:
            return f"{tamanho:.1f} {unidade}"
        tamanho /= 1024.0
    
    return f"{tamanho:.1f} TB"


def formatar_duracao(segundos: float) -> str:
    """
    Formata duração em segundos para formato legível
    
    Args:
        segundos: Duração em segundos
        
    Returns:
        Duração formatada (ex: "2m 30s")
    """
    if segundos < 60:
        return f"{segundos:.1f}s"
    
    minutos = int(segundos // 60)
    segundos_restantes = segundos % 60
    
    if minutos < 60:
        return f"{minutos}m {segundos_restantes:.0f}s"
    
    horas = int(minutos // 60)
    minutos_restantes = minutos % 60
    
    return f"{horas}h {minutos_restantes}m {segundos_restantes:.0f}s"


def contar_palavras(texto: str) -> int:
    """
    Conta palavras em um texto
    
    Args:
        texto: Texto a ser analisado
        
    Returns:
        Número de palavras
    """
    if not texto:
        return 0
    
    # Remove espaços extras e divide por espaços
    palavras = texto.strip().split()
    return len(palavras)


def estimar_tempo_audio(texto: str, palavras_por_minuto: int = 150) -> float:
    """
    Estima tempo de áudio baseado no texto
    
    Args:
        texto: Texto a ser convertido
        palavras_por_minuto: Velocidade de fala (padrão: 150 ppm)
        
    Returns:
        Tempo estimado em segundos
    """
    palavras = contar_palavras(texto)
    minutos = palavras / palavras_por_minuto
    return minutos * 60


def limpar_nome_arquivo(nome: str) -> str:
    """
    Remove caracteres inválidos de nomes de arquivo
    
    Args:
        nome: Nome original
        
    Returns:
        Nome limpo e válido para arquivo
    """
    # Caracteres inválidos no Windows
    caracteres_invalidos = '<>:"/\\|?*'
    
    nome_limpo = nome
    for char in caracteres_invalidos:
        nome_limpo = nome_limpo.replace(char, '_')
    
    # Remove espaços extras e pontos no final
    nome_limpo = nome_limpo.strip('. ')
    
    # Limita tamanho
    if len(nome_limpo) > 200:
        nome_limpo = nome_limpo[:200]
    
    return nome_limpo


def verificar_espaco_disco(caminho: Path, tamanho_necessario: int) -> bool:
    """
    Verifica se há espaço suficiente em disco
    
    Args:
        caminho: Caminho onde será salvo
        tamanho_necessario: Tamanho necessário em bytes
        
    Returns:
        True se há espaço suficiente
    """
    try:
        stat = os.statvfs(caminho) if hasattr(os, 'statvfs') else None
        if stat:
            espaco_livre = stat.f_bavail * stat.f_frsize
        else:
            # Windows
            import shutil
            espaco_livre = shutil.disk_usage(caminho)[2]
        
        return espaco_livre > tamanho_necessario
    
    except Exception as e:
        logger.warning(f"Não foi possível verificar espaço em disco: {e}")
        return True  # Assume que há espaço


class ProgressoTarefa:
    """Classe para mostrar progresso de tarefas longas"""
    
    def __init__(self, total: int, descricao: str = "Processando"):
        self.total = total
        self.atual = 0
        self.descricao = descricao
        self.inicio = time.time()
        self.ultima_atualizacao = 0
    
    def atualizar(self, incremento: int = 1):
        """Atualiza progresso"""
        self.atual += incremento
        
        # Atualizar a cada 1 segundo ou no final
        agora = time.time()
        if agora - self.ultima_atualizacao > 1.0 or self.atual >= self.total:
            self._mostrar_progresso()
            self.ultima_atualizacao = agora
    
    def _mostrar_progresso(self):
        """Mostra barra de progresso"""
        if self.total == 0:
            return
        
        percentual = (self.atual / self.total) * 100
        tempo_decorrido = time.time() - self.inicio
        
        if self.atual > 0:
            tempo_estimado = (tempo_decorrido / self.atual) * self.total
            tempo_restante = tempo_estimado - tempo_decorrido
        else:
            tempo_restante = 0
        
        barra_tamanho = 30
        preenchido = int(barra_tamanho * self.atual / self.total)
        barra = "█" * preenchido + "░" * (barra_tamanho - preenchido)
        
        print(f"\r{self.descricao}: [{barra}] {percentual:.1f}% "
              f"({self.atual}/{self.total}) "
              f"ETA: {formatar_duracao(tempo_restante)}", end="", flush=True)
        
        if self.atual >= self.total:
            print()  # Nova linha no final


def detectar_python_path() -> str:
    """
    Detecta o caminho correto do Python
    
    Returns:
        Caminho do executável Python
    """
    # Tentar diferentes caminhos
    caminhos_possiveis = [
        sys.executable,
        "python",
        "python3",
        "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\python.exe"
    ]
    
    for caminho in caminhos_possiveis:
        try:
            import subprocess
            result = subprocess.run([caminho, "--version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                logger.debug(f"Python encontrado: {caminho}")
                return caminho
        except Exception:
            continue
    
    logger.warning("Não foi possível detectar Python automaticamente")
    return "python"


def main():
    """Função de teste dos utilitários"""
    print("Testando utilitários...")
    
    # Teste de formatação
    print(f"Duração: {formatar_duracao(125.5)}")
    print(f"Palavras: {contar_palavras('Este é um teste com várias palavras.')}")
    print(f"Tempo estimado: {formatar_duracao(estimar_tempo_audio('Este é um teste'))}")
    print(f"Nome limpo: {limpar_nome_arquivo('Arquivo<>com:caracteres/inválidos.txt')}")
    
    # Teste de progresso
    progresso = ProgressoTarefa(10, "Teste")
    for i in range(10):
        time.sleep(0.1)
        progresso.atualizar()
    
    print("Testes concluídos!")


if __name__ == "__main__":
    main()
