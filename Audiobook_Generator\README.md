# 🎧 Gerador de Audiobooks Kokoro TTS

Sistema completo para converter suas notas do Obsidian em audiobooks de alta qualidade usando o modelo Kokoro TTS em português brasileiro.

## ✨ Características

- 🇧🇷 **Português Brasileiro**: Síntese de voz nativa em português do Brasil
- 🎵 **Alta Qualidade**: Usa o modelo Kokoro-82M para áudio natural
- 📝 **Processamento Inteligente**: Remove automaticamente formatações Markdown/HTML
- ⚡ **Rápido**: Processamento otimizado com suporte a GPU AMD
- 🔧 **Configurável**: Sistema de configuração flexível
- 📁 **Batch Processing**: Processa pastas inteiras automaticamente
- 🎛️ **Interface CLI**: Interface de linha de comando intuitiva

## 🚀 Instalação

### Pré-requisitos

- Python 3.9 ou superior
- Windows 10/11 (testado)
- Placa de vídeo AMD (opcional, para aceleração)

### Dependências

O sistema instala automaticamente:
- Kokoro TTS (≥0.9.4)
- PyTorch (≥2.0.0)
- TorchAudio (≥2.0.0)
- SoundFile (≥0.12.1)
- PyYAML, Click, TQDM

### Configuração Inicial

1. **Clone ou baixe o projeto**
2. **Execute a configuração inicial:**
   ```bash
   python main.py setup
   ```

## 📖 Uso

### Comando Básico

```bash
# Gerar audiobook para um arquivo específico
python main.py arquivo "caminho/para/nota.md"

# Gerar audiobooks para uma pasta inteira
python main.py pasta "caminho/para/pasta"
```

### Opções Avançadas

```bash
# Usar voz específica
python main.py arquivo nota.md --voice af_sky

# Ajustar velocidade
python main.py arquivo nota.md --speed 1.2

# Pasta de saída personalizada
python main.py pasta resumos/ --output meus_audiobooks/

# Modo verboso
python main.py -v arquivo nota.md

# Arquivo de configuração personalizado
python main.py -c config_custom.yaml pasta resumos/
```

### Comandos Utilitários

```bash
# Ver configurações atuais
python main.py config

# Configuração inicial
python main.py setup

# Ajuda
python main.py --help
```

## ⚙️ Configuração

### Arquivo de Configuração

O sistema usa um arquivo `config/config.yaml` para configurações. Exemplo:

```yaml
# Configurações do TTS
tts:
  lang_code: 'p'          # Português brasileiro
  voice: 'af_heart'       # Voz feminina padrão
  speed: 1.0              # Velocidade normal
  sample_rate: 24000      # Taxa de amostragem
  normalizar_volume: true # Normalizar volume

# Processamento
processamento:
  max_palavras_segmento: 200    # Palavras por segmento
  pausa_entre_segmentos: 0.5    # Pausa em segundos
  formato_saida: 'wav'          # Formato de saída

# Caminhos
caminhos:
  obsidian_vault: '../'
  pasta_resumos: '📝Resumos'
  pasta_saida: 'output/audiobooks'
```

### Vozes Disponíveis

- **Femininas**: `af_heart`, `af_sky`, `af_bella`
- **Masculinas**: `am_adam`, `am_michael`
- **Neutras**: `af_sarah`, `am_alex`

## 📁 Estrutura do Projeto

```
Audiobook_Generator/
├── main.py                 # Interface principal
├── requirements.txt        # Dependências
├── README.md              # Documentação
├── src/                   # Código fonte
│   ├── gerador_audiobook.py    # Gerador principal
│   ├── processador_notas.py    # Processamento de texto
│   ├── configuracao.py         # Sistema de configuração
│   └── utils.py               # Utilitários
├── config/                # Configurações
│   └── config.yaml        # Arquivo de configuração
├── output/                # Audiobooks gerados
├── temp/                  # Arquivos temporários
└── logs/                  # Logs do sistema
```

## 🎯 Exemplos de Uso

### Exemplo 1: Arquivo Individual

```bash
python main.py arquivo "../📝Resumos/Administrativo/01 - Regime Jurídico.md"
```

**Resultado:**
- Arquivo: `output/audiobooks/01 - Regime Jurídico.wav`
- Duração: ~15 minutos
- Qualidade: 24kHz, 16-bit

### Exemplo 2: Pasta Completa

```bash
python main.py pasta "../📝Resumos/Administrativo" --voice af_sky --speed 1.1
```

**Resultado:**
- Múltiplos audiobooks na pasta `output/audiobooks/`
- Voz feminina alternativa
- Velocidade ligeiramente aumentada

### Exemplo 3: Configuração Personalizada

```bash
python main.py -c minha_config.yaml pasta "../🎓Conceitos"
```

## 🔧 Solução de Problemas

### Erro: "ModuleNotFoundError: No module named 'kokoro'"

**Solução:**
```bash
pip install kokoro>=0.9.4
```

### Erro: GPU AMD não detectada

**Solução:**
- O sistema funciona perfeitamente com CPU
- Para GPU AMD, instale ROCm (Linux) ou use CPU no Windows

### Áudio muito baixo/alto

**Solução:**
Ajuste no arquivo de configuração:
```yaml
tts:
  normalizar_volume: true
  target_volume: 0.8  # Aumentar para mais volume
```

### Processamento muito lento

**Soluções:**
1. Reduza `max_palavras_segmento` para 100-150
2. Use `speed: 1.2` para áudio mais rápido
3. Processe arquivos menores individualmente

## 📊 Performance

### Benchmarks Típicos

- **Arquivo pequeno** (1000 palavras): ~30 segundos
- **Arquivo médio** (5000 palavras): ~2 minutos
- **Arquivo grande** (10000 palavras): ~4 minutos

### Qualidade do Áudio

- **Taxa de amostragem**: 24kHz
- **Formato**: WAV 16-bit
- **Qualidade**: Comparável a TTS comerciais
- **Naturalidade**: Excelente para português brasileiro

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto usa o modelo Kokoro TTS que é licenciado sob Apache 2.0.

## 🙏 Agradecimentos

- **Kokoro TTS**: Modelo de síntese de voz de alta qualidade
- **Hexgrad**: Criadores do Kokoro
- **Comunidade Python**: Bibliotecas e ferramentas utilizadas

## 📞 Suporte

Para problemas ou dúvidas:
1. Verifique a seção de solução de problemas
2. Execute `python main.py config` para verificar configurações
3. Use `python main.py -v` para logs detalhados

---

**Desenvolvido com ❤️ para estudantes e profissionais que querem transformar suas notas em audiobooks de qualidade.**
