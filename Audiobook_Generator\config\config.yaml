# Configuração do Gerador de Audiobooks Kokoro TTS
# Configurações otimizadas para vozes brasileiras naturais

# Configurações do Text-to-Speech
tts:
  lang_code: 'p'          # Portugu<PERSON><PERSON> brasileiro
  voice: 'pf_dora'        # Voz brasileira feminina nativa (mais natural)
  speed: 0.85             # Velocidade reduzida para soar mais natural
  sample_rate: 24000      # Taxa de amostragem
  normalizar_volume: true # Normalizar volume do áudio
  target_volume: 0.7      # Nível de volume alvo (0.0 - 1.0)

# Configurações de processamento
processamento:
  max_palavras_segmento: 200    # Máximo de palavras por segmento
  pausa_entre_segmentos: 0.5    # Pausa em segundos entre segmentos
  formato_saida: 'wav'          # Formato de saída (wav, mp3, flac)
  qualidade_audio: 'alta'       # Qualidade do áudio

# Caminhos do sistema
caminhos:
  obsidian_vault: '../'                    # Caminho para o Obsidian Vault
  pasta_resumos: '📝Resumos'              # Pasta com resumos
  pasta_saida: 'output/audiobooks'        # Pasta de saída
  pasta_temp: 'temp'                      # Pasta temporária
  pasta_logs: 'logs'                      # Pasta de logs

# Configurações da interface
interface:
  mostrar_progresso: true      # Mostrar barra de progresso
  nivel_log: 'INFO'           # Nível de logging (DEBUG, INFO, WARNING, ERROR)
  salvar_logs: true           # Salvar logs em arquivo
  confirmar_sobrescrita: true # Confirmar antes de sobrescrever arquivos

# Configurações avançadas
avancado:
  usar_gpu: true              # Tentar usar GPU se disponível
  batch_size: 1               # Tamanho do batch para processamento
  cache_modelos: true         # Fazer cache dos modelos carregados
  otimizar_memoria: false     # Otimizar uso de memória
