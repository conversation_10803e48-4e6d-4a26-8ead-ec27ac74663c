#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do Gerador de Audiobooks
"""

import sys
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent / "src"))

from gerador_audiobook import GeradorAudiobook
from configuracao import ConfiguradorAudiobook

def main():
    """Testa o gerador de audiobooks"""
    print("🚀 Testando Gerador de Audiobooks...")
    
    try:
        # Configurar sistema
        configurador = ConfiguradorAudiobook()
        config_tts = configurador.obter_configuracoes_tts()
        
        print(f"⚙️ Configurações TTS: {config_tts}")
        
        # Inicializar gerador
        gerador = GeradorAudiobook(config_tts)
        
        # Arquivo de teste (pequeno para teste rápido)
        arquivo_teste = Path("../🎓Conceitos/Direito Administrativo.md")
        
        if not arquivo_teste.exists():
            print(f"❌ Arquivo de teste não encontrado: {arquivo_teste}")
            print("Criando arquivo de teste...")
            
            # Criar arquivo de teste simples
            arquivo_teste = Path("temp/teste_simples.md")
            arquivo_teste.parent.mkdir(exist_ok=True)
            
            with open(arquivo_teste, 'w', encoding='utf-8') as f:
                f.write("""# Teste de Audiobook

Este é um teste simples do sistema de audiobooks.

## Características

O sistema converte notas do Obsidian em áudio usando o Kokoro TTS.

**Funcionalidades:**
- Processamento de texto
- Síntese de voz em português
- Geração de arquivos de áudio

Teste concluído com sucesso!
""")
        
        # Gerar audiobook
        pasta_saida = Path("output/teste")
        info_audiobook = gerador.gerar_audiobook_arquivo(arquivo_teste, pasta_saida)
        
        print("\n🎉 Audiobook gerado com sucesso!")
        print(f"📁 Arquivo: {info_audiobook['arquivo_saida']}")
        print(f"⏱️ Duração: {info_audiobook['duracao_formatada']}")
        print(f"📊 Palavras: {info_audiobook['palavras']}")
        print(f"🔀 Segmentos: {info_audiobook['segmentos']}")
        print(f"📏 Tamanho: {info_audiobook['tamanho_arquivo']}")
        print(f"⚡ Tempo de processamento: {info_audiobook['tempo_processamento_formatado']}")
        
        # Verificar se arquivo foi criado
        arquivo_saida = Path(info_audiobook['arquivo_saida'])
        if arquivo_saida.exists():
            print(f"✅ Arquivo de áudio criado com sucesso!")
        else:
            print(f"❌ Arquivo de áudio não foi criado")
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
