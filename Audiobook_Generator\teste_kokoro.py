#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do Kokoro TTS com Português Brasileiro
"""

import os
import sys
from pathlib import Path

def testar_kokoro_tts():
    """Testa o Kokoro TTS com texto em português brasileiro"""
    try:
        print("🔄 Importando Kokoro TTS...")
        from kokoro import KPipeline
        import soundfile as sf
        
        print("✅ Kokoro TTS importado com sucesso!")
        
        # Configurar pipeline para português brasileiro
        print("🔄 Configurando pipeline para português brasileiro...")
        pipeline = KPipeline(lang_code='p')  # 'p' = Portuguese Brazilian
        
        print("✅ Pipeline configurado!")
        
        # Texto de teste em português brasileiro
        texto_teste = """
        Olá! Este é um teste do sistema de audiobooks em português do Brasil.
        O Kokoro TTS está funcionando corretamente para converter suas notas do Obsidian em audiobooks.
        Agora você pode ouvir seus resumos de estudo enquanto faz outras atividades.
        """
        
        print("🔄 Gerando áudio de teste...")
        
        # Gerar áudio
        generator = pipeline(
            texto_teste,
            voice='af_heart',  # Voz padrão
            speed=1.0,
            split_pattern=r'\n+'
        )
        
        # Salvar primeiro segmento de áudio
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        for i, (gs, ps, audio) in enumerate(generator):
            arquivo_saida = output_dir / f"teste_kokoro_{i}.wav"
            sf.write(str(arquivo_saida), audio, 24000)
            print(f"✅ Áudio salvo: {arquivo_saida}")
            
            if i == 0:  # Salvar apenas o primeiro segmento para teste
                break
        
        print("🎉 Teste do Kokoro TTS concluído com sucesso!")
        print(f"📁 Arquivo de teste salvo em: {output_dir / 'teste_kokoro_0.wav'}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("💡 Certifique-se de que o Kokoro TTS está instalado corretamente")
        return False
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Iniciando teste do Kokoro TTS...")
    sucesso = testar_kokoro_tts()
    
    if sucesso:
        print("\n✅ Kokoro TTS está pronto para uso!")
    else:
        print("\n❌ Problemas detectados no Kokoro TTS")
        sys.exit(1)
