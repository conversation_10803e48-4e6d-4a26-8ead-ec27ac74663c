#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Geração de Audiobooks - Interface Principal
Converte notas do Obsidian em audiobooks usando Kokoro TTS
"""

import sys
import os
import click
import logging
from pathlib import Path
from typing import Optional

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent / "src"))

from gerador_audiobook import GeradorAudiobook
from configuracao import Configu<PERSON>rAudiobook
from utils import configurar_logging, validar_caminho, criar_estrutura_diretorios, formatar_duracao

# Configuração global
VERSAO = "1.0.0"
NOME_APP = "Gerador de Audiobooks Kokoro TTS"


@click.group()
@click.version_option(version=VERSAO, prog_name=NOME_APP)
@click.option('--config', '-c', type=click.Path(exists=True), 
              help='Caminho para arquivo de configuração personalizado')
@click.option('--verbose', '-v', is_flag=True, help='Modo verboso (debug)')
@click.option('--quiet', '-q', is_flag=True, help='Modo silencioso (apenas erros)')
@click.pass_context
def cli(ctx, config, verbose, quiet):
    """
    Sistema de Geração de Audiobooks usando Kokoro TTS
    
    Converte suas notas do Obsidian em audiobooks de alta qualidade
    em português brasileiro.
    """
    # Configurar contexto
    ctx.ensure_object(dict)
    
    # Configurar logging
    if verbose:
        nivel_log = 'DEBUG'
    elif quiet:
        nivel_log = 'ERROR'
    else:
        nivel_log = 'INFO'
    
    configurar_logging(nivel_log)
    
    # Carregar configurações
    arquivo_config = Path(config) if config else None
    configurador = ConfiguradorAudiobook(arquivo_config)
    
    # Validar configurações
    erros = configurador.validar_configuracoes()
    if erros:
        click.echo("❌ Erros nas configurações:", err=True)
        for erro in erros:
            click.echo(f"   - {erro}", err=True)
        sys.exit(1)
    
    # Armazenar no contexto
    ctx.obj['configurador'] = configurador
    ctx.obj['config_tts'] = configurador.obter_configuracoes_tts()


@cli.command()
@click.argument('arquivo', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), 
              help='Pasta de saída (padrão: output/audiobooks)')
@click.option('--voice', help='Voz a ser usada (ex: af_heart, af_sky)')
@click.option('--speed', type=float, help='Velocidade da fala (0.5 - 2.0)')
@click.pass_context
def arquivo(ctx, arquivo, output, voice, speed):
    """
    Gera audiobook para um arquivo específico
    
    ARQUIVO: Caminho para o arquivo .md do Obsidian
    """
    try:
        # Obter configurações
        configurador = ctx.obj['configurador']
        config_tts = ctx.obj['config_tts'].copy()
        
        # Aplicar opções da linha de comando
        if voice:
            config_tts['voice'] = voice
        if speed:
            config_tts['speed'] = speed
        
        # Definir pasta de saída
        if output:
            pasta_saida = Path(output)
        else:
            caminhos = configurador.obter_caminhos()
            pasta_saida = caminhos['pasta_saida']
        
        # Validar arquivo
        arquivo_path = validar_caminho(arquivo, deve_existir=True)
        if not arquivo_path.suffix.lower() == '.md':
            click.echo("❌ Arquivo deve ter extensão .md", err=True)
            sys.exit(1)
        
        click.echo(f"🚀 Gerando audiobook para: {arquivo_path.name}")
        click.echo(f"📁 Pasta de saída: {pasta_saida}")
        click.echo(f"🎵 Voz: {config_tts['voice']}")
        click.echo(f"⚡ Velocidade: {config_tts['speed']}x")
        
        # Gerar audiobook
        gerador = GeradorAudiobook(config_tts)
        info_audiobook = gerador.gerar_audiobook_arquivo(arquivo_path, pasta_saida)
        
        # Mostrar resultados
        click.echo("\n🎉 Audiobook gerado com sucesso!")
        click.echo(f"📁 Arquivo: {info_audiobook['arquivo_saida']}")
        click.echo(f"⏱️ Duração: {info_audiobook['duracao_formatada']}")
        click.echo(f"📊 Palavras: {info_audiobook['palavras']:,}")
        click.echo(f"🔀 Segmentos: {info_audiobook['segmentos']}")
        click.echo(f"📏 Tamanho: {info_audiobook['tamanho_arquivo']}")
        click.echo(f"⚡ Tempo de processamento: {info_audiobook['tempo_processamento_formatado']}")
        
    except Exception as e:
        click.echo(f"❌ Erro: {e}", err=True)
        if ctx.obj.get('verbose'):
            import traceback
            traceback.print_exc()
        sys.exit(1)


@cli.command()
@click.argument('pasta', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), 
              help='Pasta de saída (padrão: output/audiobooks)')
@click.option('--voice', help='Voz a ser usada')
@click.option('--speed', type=float, help='Velocidade da fala')
@click.option('--filtro', default='*.md', help='Filtro de arquivos (padrão: *.md)')
@click.pass_context
def pasta(ctx, pasta, output, voice, speed, filtro):
    """
    Gera audiobooks para todos os arquivos de uma pasta
    
    PASTA: Caminho para a pasta com arquivos .md
    """
    try:
        # Obter configurações
        configurador = ctx.obj['configurador']
        config_tts = ctx.obj['config_tts'].copy()
        
        # Aplicar opções da linha de comando
        if voice:
            config_tts['voice'] = voice
        if speed:
            config_tts['speed'] = speed
        
        # Definir pasta de saída
        if output:
            pasta_saida = Path(output)
        else:
            caminhos = configurador.obter_caminhos()
            pasta_saida = caminhos['pasta_saida']
        
        # Validar pasta
        pasta_origem = validar_caminho(pasta, deve_existir=True)
        
        click.echo(f"🚀 Processando pasta: {pasta_origem}")
        click.echo(f"📁 Pasta de saída: {pasta_saida}")
        click.echo(f"🎵 Voz: {config_tts['voice']}")
        click.echo(f"⚡ Velocidade: {config_tts['speed']}x")
        click.echo(f"🔍 Filtro: {filtro}")
        
        # Confirmar se há muitos arquivos
        arquivos = list(pasta_origem.glob(filtro))
        if len(arquivos) > 10:
            if not click.confirm(f"Encontrados {len(arquivos)} arquivos. Continuar?"):
                click.echo("Operação cancelada.")
                return
        
        # Gerar audiobooks
        gerador = GeradorAudiobook(config_tts)
        audiobooks = gerador.gerar_audiobook_pasta(pasta_origem, pasta_saida)
        
        # Mostrar resultados
        if audiobooks:
            total_duracao = sum(ab['duracao_audio'] for ab in audiobooks)
            total_palavras = sum(ab['palavras'] for ab in audiobooks)
            
            click.echo(f"\n🎉 Processamento concluído!")
            click.echo(f"📚 Audiobooks gerados: {len(audiobooks)}")
            click.echo(f"⏱️ Duração total: {formatar_duracao(total_duracao)}")
            click.echo(f"📝 Total de palavras: {total_palavras:,}")
            click.echo(f"📁 Pasta de saída: {pasta_saida}")
        else:
            click.echo("❌ Nenhum audiobook foi gerado.")
        
    except Exception as e:
        click.echo(f"❌ Erro: {e}", err=True)
        if ctx.obj.get('verbose'):
            import traceback
            traceback.print_exc()
        sys.exit(1)


@cli.command()
@click.pass_context
def config(ctx):
    """
    Mostra configurações atuais do sistema
    """
    configurador = ctx.obj['configurador']
    
    click.echo(f"⚙️ Configurações do {NOME_APP}")
    click.echo("=" * 50)
    
    # Configurações TTS
    click.echo("\n🎵 Text-to-Speech:")
    click.echo(f"   Idioma: {configurador.obter('tts.lang_code')} (Português BR)")
    click.echo(f"   Voz: {configurador.obter('tts.voice')}")
    click.echo(f"   Velocidade: {configurador.obter('tts.speed')}x")
    click.echo(f"   Taxa de amostragem: {configurador.obter('tts.sample_rate')} Hz")
    
    # Configurações de processamento
    click.echo("\n⚙️ Processamento:")
    click.echo(f"   Palavras por segmento: {configurador.obter('processamento.max_palavras_segmento')}")
    click.echo(f"   Pausa entre segmentos: {configurador.obter('processamento.pausa_entre_segmentos')}s")
    click.echo(f"   Formato de saída: {configurador.obter('processamento.formato_saida')}")
    
    # Caminhos
    click.echo("\n📁 Caminhos:")
    caminhos = configurador.obter_caminhos()
    for nome, caminho in caminhos.items():
        status = "✅" if caminho.exists() else "❌"
        click.echo(f"   {nome}: {caminho} {status}")
    
    # Vozes disponíveis
    click.echo("\n🎤 Vozes disponíveis:")
    vozes = configurador.listar_vozes_disponiveis()
    for categoria, lista_vozes in vozes.items():
        click.echo(f"   {categoria.title()}: {', '.join(lista_vozes)}")


@cli.command()
@click.pass_context
def setup(ctx):
    """
    Configura o sistema pela primeira vez
    """
    click.echo(f"🛠️ Configuração inicial do {NOME_APP}")
    click.echo("=" * 50)
    
    configurador = ctx.obj['configurador']
    
    # Criar estrutura de diretórios
    click.echo("\n📁 Criando estrutura de diretórios...")
    caminhos = configurador.obter_caminhos()
    
    diretorios = ['pasta_saida', 'pasta_temp', 'pasta_logs']
    for dir_nome in diretorios:
        caminho = caminhos[dir_nome]
        caminho.mkdir(parents=True, exist_ok=True)
        click.echo(f"   ✅ {dir_nome}: {caminho}")
    
    # Criar arquivo de configuração de exemplo
    click.echo("\n📝 Criando arquivo de configuração de exemplo...")
    configurador.criar_configuracao_exemplo()
    
    # Testar TTS
    click.echo("\n🎵 Testando Kokoro TTS...")
    try:
        config_tts = configurador.obter_configuracoes_tts()
        gerador = GeradorAudiobook(config_tts)
        click.echo("   ✅ Kokoro TTS funcionando!")
    except Exception as e:
        click.echo(f"   ❌ Erro no TTS: {e}")
    
    click.echo("\n🎉 Configuração inicial concluída!")
    click.echo("💡 Dicas:")
    click.echo("   - Use 'main.py config' para ver as configurações")
    click.echo("   - Use 'main.py arquivo <arquivo.md>' para gerar um audiobook")
    click.echo("   - Use 'main.py pasta <pasta>' para processar uma pasta inteira")


if __name__ == '__main__':
    cli()
