
# Sinopse: Converte o conteúdo de um arquivo de texto (.md ou .txt) para um arquivo de áudio (.wav).

[CmdletBinding()]
param (
    [Parameter(Mandatory=$true, HelpMessage='Digite o caminho completo para o arquivo de texto de entrada (ex: C:\Obsidian Vault\minha_nota.md)')]
    [string]$ArquivoDeTexto,

    [Parameter(Mandatory=$true, HelpMessage='Digite o caminho completo para o arquivo de áudio de saída (ex: C:\Obsidian Vault\meu_audio.wav)')]
    [string]$ArquivoDeAudio
)

Try {
    # Verifica se o arquivo de entrada existe
    if (-not (Test-Path -Path $ArquivoDeTexto -PathType Leaf)) {
        throw "O arquivo de texto '$ArquivoDeTexto' não foi encontrado."
    }

    # Carrega o assembly de fala do .NET
    Add-Type -AssemblyName System.Speech

    # Cria o objeto sintetizador de voz
    $synthesizer = New-Object System.Speech.Synthesis.SpeechSynthesizer

    # Configura a saída para um arquivo .wav
    $synthesizer.SetOutputToWaveFile($ArquivoDeAudio)

    # Lê todo o conteúdo do arquivo de texto
    $texto = Get-Content -Path $ArquivoDeTexto -Raw

    # Fala (grava) o texto para o arquivo
    Write-Host "Iniciando a conversão... Isso pode levar alguns instantes."
    $synthesizer.Speak($texto)

    # Libera os recursos
    $synthesizer.Dispose()

    Write-Host "Sucesso! O arquivo de áudio '$ArquivoDeAudio' foi criado."
}
Catch {
    Write-Error "Ocorreu um erro: $_"
}

# Mantém a janela aberta para o usuário ler a saída.
Write-Host "Pressione Enter para sair."
Read-Host
