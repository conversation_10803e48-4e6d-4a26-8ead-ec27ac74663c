# ✅ Checklist Rápido - Sistema de Audiobooks

## 🚀 INSTALAÇÃO (Faça uma vez só)

### ☑️ Pré-requisitos
- [ ] Python instalado (digite `python --version` no cmd)
- [ ] Pasta do sistema localizada: `C:\Obsidian Vault\Audiobook_Generator`

### ☑️ Configuração
1. [ ] Abrir terminal na pasta do sistema
2. [ ] Executar: `python main.py setup`
3. [ ] Aguardar download e instalação (2-3 minutos)
4. [ ] Ver mensagem "Configuração inicial concluída!"

---

## 🎵 USO BÁSICO

### ☑️ Um Arquivo
```bash
python main.py arquivo "CAMINHO_DO_SEU_ARQUIVO.md"
```
- [ ] Substituir pelo caminho real do arquivo
- [ ] Aguardar processamento
- [ ] Verificar arquivo em `output\audiobooks\`

### ☑️ Uma Pasta Inteira
```bash
python main.py pasta "CAMINHO_DA_SUA_PASTA"
```
- [ ] Substituir pelo caminho real da pasta
- [ ] Confirmar se tiver muitos arquivos
- [ ] Aguardar processamento completo

---

## 🎛️ PERSONALIZAÇÕES

### ☑️ Vozes Disponíveis
**Femininas:**
- [ ] `af_heart` (padrão, suave)
- [ ] `af_sky` (clara)
- [ ] `af_bella` (expressiva)

**Masculinas:**
- [ ] `am_adam` (grave)
- [ ] `am_michael` (médio)

**Como usar:**
```bash
python main.py arquivo "arquivo.md" --voice am_adam
```

### ☑️ Velocidades
- [ ] `--speed 0.8` (mais devagar)
- [ ] `--speed 1.0` (normal - padrão)
- [ ] `--speed 1.2` (mais rápido)
- [ ] `--speed 1.5` (bem rápido)

**Como usar:**
```bash
python main.py arquivo "arquivo.md" --speed 1.2
```

### ☑️ Combinando Opções
```bash
python main.py arquivo "arquivo.md" --voice af_sky --speed 1.1
```

---

## 📂 ORGANIZAÇÃO

### ☑️ Onde Encontrar os Audiobooks
- [ ] Pasta: `C:\Obsidian Vault\Audiobook_Generator\output\audiobooks\`
- [ ] Arquivos no formato `.wav`
- [ ] Mesmo nome do arquivo original

### ☑️ Como Organizar
1. [ ] Criar pastas por matéria
2. [ ] Copiar audiobooks para as pastas certas
3. [ ] Renomear se necessário
4. [ ] Sincronizar com celular/nuvem

---

## 📱 COMO OUVIR

### ☑️ No Computador
- [ ] Clicar duas vezes no arquivo `.wav`
- [ ] Usar Windows Media Player ou similar

### ☑️ No Celular
- [ ] Copiar arquivos para o celular
- [ ] Usar app de música favorito
- [ ] Ou app específico de audiobooks

### ☑️ No Carro
- [ ] Pendrive com os arquivos
- [ ] Ou Bluetooth do celular
- [ ] Ou cabo auxiliar

---

## 🔧 COMANDOS ÚTEIS

### ☑️ Verificar Configurações
```bash
python main.py config
```

### ☑️ Ajuda
```bash
python main.py --help
```

### ☑️ Reconfigurar Sistema
```bash
python main.py setup
```

---

## ❓ PROBLEMAS COMUNS

### ☑️ "Comando não encontrado"
**Solução:**
- [ ] Verificar se está na pasta certa
- [ ] Executar: `cd "C:\Obsidian Vault\Audiobook_Generator"`

### ☑️ "Arquivo não encontrado"
**Soluções:**
- [ ] Usar caminho completo do arquivo
- [ ] Verificar se arquivo existe
- [ ] Usar aspas duplas no caminho

### ☑️ "Processamento lento"
**Soluções:**
- [ ] Dividir arquivos grandes
- [ ] Processar um por vez
- [ ] Fechar outros programas

### ☑️ "Áudio muito baixo"
**Soluções:**
- [ ] Aumentar volume no player
- [ ] Volume está normalizado automaticamente

---

## 🎯 FLUXO DE TRABALHO RECOMENDADO

### ☑️ Primeira Vez
1. [ ] Fazer instalação completa
2. [ ] Testar com arquivo pequeno
3. [ ] Ajustar voz e velocidade preferidas
4. [ ] Processar uma matéria completa
5. [ ] Organizar audiobooks

### ☑️ Uso Diário
1. [ ] Criar/atualizar resumos no Obsidian
2. [ ] Gerar audiobooks dos novos/atualizados
3. [ ] Copiar para celular
4. [ ] Ouvir durante atividades rotineiras
5. [ ] Revisar constantemente

---

## 📊 TEMPOS ESPERADOS

### ☑️ Processamento
- [ ] Arquivo pequeno (1-2 páginas): ~30 segundos
- [ ] Arquivo médio (5-10 páginas): ~2 minutos  
- [ ] Arquivo grande (15+ páginas): ~5 minutos

### ☑️ Duração dos Audiobooks
- [ ] 1000 palavras ≈ 7-8 minutos de áudio
- [ ] 3000 palavras ≈ 20-25 minutos de áudio
- [ ] 5000 palavras ≈ 35-40 minutos de áudio

---

## 🎉 CHECKLIST DE SUCESSO

### ☑️ Sistema Funcionando
- [ ] Instalação concluída sem erros
- [ ] Teste básico funcionou
- [ ] Audiobook gerado com qualidade
- [ ] Consegue ouvir no celular/computador

### ☑️ Produtividade
- [ ] Biblioteca de audiobooks criada
- [ ] Rotina de estudo com áudio estabelecida
- [ ] Aproveitando tempos "mortos" para estudar
- [ ] Revisões constantes com audiobooks

### ☑️ Organização
- [ ] Audiobooks organizados por matéria
- [ ] Sincronização com dispositivos móveis
- [ ] Backup dos arquivos importantes
- [ ] Sistema de nomenclatura consistente

---

## 🏆 RESULTADO FINAL

Quando tudo estiver funcionando, você terá:

✅ **Biblioteca completa** de audiobooks dos seus resumos
✅ **Estudo em qualquer lugar** sem precisar ler
✅ **Revisões constantes** aproveitando tempo perdido
✅ **Melhor fixação** do conteúdo através da repetição
✅ **Mais horas de estudo** por dia

---

## 📞 COMANDOS DE EMERGÊNCIA

Se nada funcionar, execute na ordem:

1. **Reiniciar tudo:**
   ```bash
   cd "C:\Obsidian Vault\Audiobook_Generator"
   python main.py setup
   ```

2. **Teste simples:**
   ```bash
   python main.py arquivo "teste_final.md"
   ```

3. **Verificar configurações:**
   ```bash
   python main.py config
   ```

**Se ainda não funcionar:** Reinicie o computador e tente novamente.

---

**🎯 Agora é só seguir o checklist e começar a estudar com audiobooks!**

**Boa sorte nos seus concursos! 🍀📚🎧**
