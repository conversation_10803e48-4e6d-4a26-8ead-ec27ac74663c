#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Processador de Notas Obsidian para Audiobooks
Limpa formatações HTML/CSS/Markdown e prepara texto para TTS
"""

import re
import os
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProcessadorNotasObsidian:
    """
    Processador especializado para notas do Obsidian
    Remove formatações e prepara texto para conversão TTS
    """
    
    def __init__(self):
        """Inicializa o processador com padrões de limpeza"""
        self.padroes_limpeza = self._configurar_padroes_limpeza()
        self.padroes_estrutura = self._configurar_padroes_estrutura()
    
    def _configurar_padroes_limpeza(self) -> List[Tuple[str, str]]:
        """Configura padrões regex para limpeza de formatações"""
        return [
            # Tags HTML/CSS completas
            (r'<span[^>]*class="[^"]*"[^>]*>(.*?)</span>', r'\1'),
            (r'<span[^>]*style="[^"]*"[^>]*>(.*?)</span>', r'\1'),
            (r'<[^>]+>', ''),  # Qualquer tag HTML restante

            # Formatações Markdown
            (r'\*\*\*(.+?)\*\*\*', r'\1'),  # Bold + Italic
            (r'\*\*(.+?)\*\*', r'\1'),      # Bold
            (r'\*(.+?)\*', r'\1'),          # Italic
            (r'`(.+?)`', r'\1'),            # Code inline
            (r'~~(.+?)~~', r'\1'),          # Strikethrough

            # Títulos Markdown - remover completamente as marcações
            (r'^#{1,6}\s*(.+)$', r'\1'),    # Headers

            # Links e referências
            (r'\[([^\]]+)\]\([^\)]+\)', r'\1'),  # Links [texto](url)
            (r'\[\[([^\]]+)\]\]', r'\1'),        # Links internos [[texto]]

            # Separadores - remover completamente
            (r'^\s*[-*_]{3,}\s*$', ''),     # Separadores horizontais
            (r'^\s*\*\*\*\s*$', ''),       # Separadores com asteriscos

            # Listas - manter conteúdo, remover marcadores
            (r'^\s*[-*+]\s+', ''),          # Marcadores de lista
            (r'^\s*\d+\.\s+', ''),          # Listas numeradas

            # Espaços e quebras excessivas
            (r'\n\n\n+', '\n\n'),           # Múltiplas quebras de linha
            (r'[ \t]+', ' '),               # Múltiplos espaços
        ]
    
    def _configurar_padroes_estrutura(self) -> List[Tuple[str, str]]:
        """Configura padrões para preservar estrutura hierárquica"""
        return [
            # Normalizar quebras de linha
            (r'\n\n+', '\n\n'),  # Máximo duas quebras consecutivas

            # Limpar linhas vazias com apenas espaços
            (r'^\s*$', ''),

            # Limpar início e fim
            (r'^\s+', ''),
            (r'\s+$', ''),

            # Garantir ponto final em frases
            (r'([^.!?])\n', r'\1.\n'),
        ]
    
    def limpar_texto(self, texto: str) -> str:
        """
        Remove todas as formatações do texto mantendo conteúdo

        Args:
            texto: Texto original com formatações

        Returns:
            Texto limpo sem formatações
        """
        texto_limpo = texto

        # Aplicar padrões de limpeza
        for padrao, substituicao in self.padroes_limpeza:
            texto_limpo = re.sub(padrao, substituicao, texto_limpo, flags=re.MULTILINE)

        # Aplicar padrões de estrutura
        for padrao, substituicao in self.padroes_estrutura:
            texto_limpo = re.sub(padrao, substituicao, texto_limpo, flags=re.MULTILINE)

        # Limpeza final específica
        # Remover linhas que são apenas marcações de título
        linhas = texto_limpo.split('\n')
        linhas_limpas = []

        for linha in linhas:
            linha = linha.strip()
            # Pular linhas vazias ou que são apenas marcações
            if linha and not re.match(r'^#{1,6}', linha):
                linhas_limpas.append(linha)

        return '\n\n'.join(linhas_limpas).strip()
    
    def processar_arquivo(self, caminho_arquivo: Path) -> Dict[str, str]:
        """
        Processa um arquivo Markdown individual
        
        Args:
            caminho_arquivo: Caminho para o arquivo .md
            
        Returns:
            Dicionário com informações do arquivo processado
        """
        try:
            # Ler arquivo
            with open(caminho_arquivo, 'r', encoding='utf-8') as f:
                conteudo_original = f.read()
            
            # Limpar texto
            conteudo_limpo = self.limpar_texto(conteudo_original)
            
            # Informações do arquivo
            info_arquivo = {
                'nome': caminho_arquivo.stem,
                'caminho_original': str(caminho_arquivo),
                'conteudo_original': conteudo_original,
                'conteudo_limpo': conteudo_limpo,
                'tamanho_original': len(conteudo_original),
                'tamanho_limpo': len(conteudo_limpo),
                'palavras': len(conteudo_limpo.split()),
                'linhas': len(conteudo_limpo.split('\n'))
            }
            
            logger.info(f"Processado: {caminho_arquivo.name} - {info_arquivo['palavras']} palavras")
            
            return info_arquivo
            
        except Exception as e:
            logger.error(f"Erro ao processar {caminho_arquivo}: {e}")
            raise
    
    def processar_pasta(self, pasta_origem: Path, filtro_arquivos: str = "*.md") -> List[Dict[str, str]]:
        """
        Processa todos os arquivos Markdown de uma pasta
        
        Args:
            pasta_origem: Pasta contendo arquivos .md
            filtro_arquivos: Padrão de filtro para arquivos
            
        Returns:
            Lista com informações de todos os arquivos processados
        """
        if not pasta_origem.exists():
            raise FileNotFoundError(f"Pasta não encontrada: {pasta_origem}")
        
        # Encontrar arquivos
        arquivos_md = list(pasta_origem.glob(filtro_arquivos))
        
        if not arquivos_md:
            logger.warning(f"Nenhum arquivo encontrado em: {pasta_origem}")
            return []
        
        logger.info(f"Encontrados {len(arquivos_md)} arquivos para processar")
        
        # Processar cada arquivo
        arquivos_processados = []
        for arquivo in sorted(arquivos_md):
            try:
                info_arquivo = self.processar_arquivo(arquivo)
                arquivos_processados.append(info_arquivo)
            except Exception as e:
                logger.error(f"Falha ao processar {arquivo}: {e}")
                continue
        
        logger.info(f"Processados {len(arquivos_processados)} arquivos com sucesso")
        
        return arquivos_processados
    
    def dividir_texto_em_segmentos(self, texto: str, max_palavras: int = 200) -> List[str]:
        """
        Divide texto em segmentos menores para processamento TTS
        
        Args:
            texto: Texto a ser dividido
            max_palavras: Máximo de palavras por segmento
            
        Returns:
            Lista de segmentos de texto
        """
        # Dividir por parágrafos primeiro
        paragrafos = [p.strip() for p in texto.split('\n\n') if p.strip()]
        
        segmentos = []
        segmento_atual = ""
        palavras_atual = 0
        
        for paragrafo in paragrafos:
            palavras_paragrafo = len(paragrafo.split())
            
            # Se o parágrafo sozinho excede o limite, dividir por frases
            if palavras_paragrafo > max_palavras:
                if segmento_atual:
                    segmentos.append(segmento_atual.strip())
                    segmento_atual = ""
                    palavras_atual = 0
                
                # Dividir parágrafo por frases
                frases = re.split(r'[.!?]+', paragrafo)
                for frase in frases:
                    frase = frase.strip()
                    if not frase:
                        continue
                    
                    palavras_frase = len(frase.split())
                    
                    if palavras_atual + palavras_frase > max_palavras and segmento_atual:
                        segmentos.append(segmento_atual.strip())
                        segmento_atual = frase + "."
                        palavras_atual = palavras_frase
                    else:
                        segmento_atual += " " + frase + "." if segmento_atual else frase + "."
                        palavras_atual += palavras_frase
            
            # Parágrafo normal
            elif palavras_atual + palavras_paragrafo > max_palavras and segmento_atual:
                segmentos.append(segmento_atual.strip())
                segmento_atual = paragrafo
                palavras_atual = palavras_paragrafo
            else:
                segmento_atual += "\n\n" + paragrafo if segmento_atual else paragrafo
                palavras_atual += palavras_paragrafo
        
        # Adicionar último segmento
        if segmento_atual:
            segmentos.append(segmento_atual.strip())
        
        return [s for s in segmentos if s.strip()]
    
    def salvar_texto_limpo(self, info_arquivo: Dict[str, str], pasta_destino: Path) -> Path:
        """
        Salva texto limpo em arquivo TXT
        
        Args:
            info_arquivo: Informações do arquivo processado
            pasta_destino: Pasta onde salvar o arquivo limpo
            
        Returns:
            Caminho do arquivo salvo
        """
        pasta_destino.mkdir(parents=True, exist_ok=True)
        
        arquivo_destino = pasta_destino / f"{info_arquivo['nome']}_limpo.txt"
        
        with open(arquivo_destino, 'w', encoding='utf-8') as f:
            f.write(info_arquivo['conteudo_limpo'])
        
        logger.info(f"Texto limpo salvo: {arquivo_destino}")
        
        return arquivo_destino


def main():
    """Função principal para teste do módulo"""
    # Teste básico
    processador = ProcessadorNotasObsidian()
    
    # Texto de exemplo
    texto_exemplo = """
    ## Regime Jurídico Administrativo
    
    ### Definição
    <span class="definicao">O Regime Jurídico Administrativo é o conjunto harmônico de normas</span>
    
    **Características Principais:**
    * Supremacia do Interesse Público
    * Indisponibilidade do Interesse Público
    
    ---
    
    ### Atenção!
    <span class="atencao">Não existe hierarquia entre regras e princípios.</span>
    """
    
    texto_limpo = processador.limpar_texto(texto_exemplo)
    print("Texto Original:")
    print(texto_exemplo)
    print("\n" + "="*50 + "\n")
    print("Texto Limpo:")
    print(texto_limpo)


if __name__ == "__main__":
    main()
